# Clean run script for open-codex with Gemini
Write-Host "🧹 Starting clean open-codex with Gemini..." -ForegroundColor Green

# Set environment variable
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"

# Clear any existing config conflicts
$codexDir = Join-Path $env:USERPROFILE ".codex"
if (Test-Path $codexDir) {
    Remove-Item $codexDir -Recurse -Force
    Write-Host "✓ Cleared existing .codex directory" -ForegroundColor Yellow
}

# Create fresh config directory
New-Item -ItemType Directory -Path $codexDir -Force | Out-Null

# Create minimal config for Gemini
$configPath = Join-Path $codexDir "config.json"
$config = @{
    provider = "gemini"
    model = "gemini-2.0-flash"
} | ConvertTo-Json

Set-Content -Path $configPath -Value $config -Encoding UTF8
Write-Host "✓ Created fresh config: $configPath" -ForegroundColor Green

# Test the CLI
Write-Host "`n🚀 Testing CLI..." -ForegroundColor Cyan
Write-Host "API Key: $($env:GOOGLE_GENERATIVE_AI_API_KEY.Substring(0,10))..." -ForegroundColor Gray

# Run with a simple test
open-codex --provider gemini "Hello, what AI model are you?"
