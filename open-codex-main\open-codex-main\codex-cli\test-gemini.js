// Simple test script to verify Gemini integration
process.env.GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs";

console.log("Testing Gemini API integration...");
console.log("API Key set:", process.env.GOOGLE_GENERATIVE_AI_API_KEY ? "✓" : "✗");

// Import and test the config loading
import { loadConfig } from './src/utils/config.js';

try {
  const config = loadConfig(undefined, undefined, {
    provider: 'gemini',
    cwd: process.cwd()
  });
  
  console.log("Config loaded successfully:");
  console.log("- Provider:", config.provider);
  console.log("- Model:", config.model);
  console.log("- API Key:", config.apiKey ? "✓ Set" : "✗ Missing");
  console.log("- Base URL:", config.baseURL);
} catch (error) {
  console.error("Error loading config:", error.message);
}
