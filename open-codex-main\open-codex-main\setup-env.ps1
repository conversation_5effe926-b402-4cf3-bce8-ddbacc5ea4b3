# Setup Environment Variables for Enhanced AI Agent
Write-Host "🔧 Setting up Enhanced AI Development Agent Environment" -ForegroundColor Cyan
Write-Host "=====================================================" -ForegroundColor Cyan

# Set the Gemini API key
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"

Write-Host "✅ Environment variables set successfully!" -ForegroundColor Green
Write-Host "🔑 GOOGLE_GENERATIVE_AI_API_KEY: Set" -ForegroundColor Green

Write-Host "`n🚀 Enhanced AI Agent is ready to use!" -ForegroundColor Magenta
Write-Host "You can now run commands like:" -ForegroundColor White
Write-Host "  node .\codex-cli\dist\cli.js --provider gemini 'Create a hello world app'" -ForegroundColor Gray
Write-Host "  node .\codex-cli\dist\cli.js --provider gemini --quiet 'List files in current directory'" -ForegroundColor Gray
Write-Host "  node .\codex-cli\dist\cli.js --provider gemini 'Mujhe ek simple calculator chahiye'" -ForegroundColor Gray

Write-Host "`n💡 Available Features:" -ForegroundColor Yellow
Write-Host "  • Chain-of-thought reasoning with self-critique" -ForegroundColor Gray
Write-Host "  • Advanced file operations (create/read/write/delete/search)" -ForegroundColor Gray
Write-Host "  • Code analysis with AST parsing (20+ languages)" -ForegroundColor Gray
Write-Host "  • Web search integration for documentation" -ForegroundColor Gray
Write-Host "  • Multi-step workflow execution" -ForegroundColor Gray
Write-Host "  • Natural language processing (Hindi/English)" -ForegroundColor Gray
Write-Host "  • Autonomous debugging and error fixing" -ForegroundColor Gray
Write-Host "  • Context-aware refactoring and optimization" -ForegroundColor Gray

Write-Host "`n🎯 Ready for production use!" -ForegroundColor Green
