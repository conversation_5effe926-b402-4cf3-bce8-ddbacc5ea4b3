# Test script for command execution fixes
Write-Host "🔧 TESTING COMMAND EXECUTION FIXES" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# Set environment variable
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"
Write-Host "✓ Environment variable set" -ForegroundColor Green

# Test basic Windows commands directly first
Write-Host "`n🧪 Testing basic Windows commands..." -ForegroundColor Yellow

Write-Host "Testing 'echo hello':" -ForegroundColor Gray
cmd /c echo hello

Write-Host "`nTesting 'dir':" -ForegroundColor Gray  
cmd /c dir | Select-Object -First 3

Write-Host "`nTesting 'cd':" -ForegroundColor Gray
cmd /c cd

# Now test through the CLI
Write-Host "`n🤖 Testing through Open-Codex CLI..." -ForegroundColor Yellow

# Test 1: Simple echo command
Write-Host "`n📝 TEST 1: Echo Command" -ForegroundColor Cyan
try {
    $result = & node dist/cli.js --provider gemini --quiet "Use the shell command to echo 'Hello World'" 2>&1
    Write-Host "Result: $result" -ForegroundColor Gray
    if ($result -and !$result.Contains("ENOENT")) {
        Write-Host "✅ Echo test passed" -ForegroundColor Green
    } else {
        Write-Host "❌ Echo test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Echo test error: $_" -ForegroundColor Red
}

# Test 2: Directory listing
Write-Host "`n📁 TEST 2: Directory Listing" -ForegroundColor Cyan
try {
    $result = & node dist/cli.js --provider gemini --quiet "List the files in the current directory using shell command" 2>&1
    Write-Host "Result: $result" -ForegroundColor Gray
    if ($result -and !$result.Contains("ENOENT")) {
        Write-Host "✅ Directory listing test passed" -ForegroundColor Green
    } else {
        Write-Host "❌ Directory listing test failed" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Directory listing test error: $_" -ForegroundColor Red
}

# Test 3: Simple math
Write-Host "`n🔢 TEST 3: Simple Math (No Shell)" -ForegroundColor Cyan
try {
    $result = & node dist/cli.js --provider gemini --quiet "What is 5 + 3? Just give me the number." 2>&1
    Write-Host "Result: $result" -ForegroundColor Gray
    if ($result -and $result.Contains("8")) {
        Write-Host "✅ Math test passed" -ForegroundColor Green
    } else {
        Write-Host "⚠️ Math test unclear result" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Math test error: $_" -ForegroundColor Red
}

Write-Host "`n🏁 COMMAND EXECUTION TEST COMPLETE" -ForegroundColor Cyan
