import { isLoggingEnabled, log } from "./log.js";

export interface ThoughtStep {
  id: string;
  type: "problem_analysis" | "solution_planning" | "implementation" | "validation" | "reflection";
  description: string;
  reasoning: string;
  confidence: number; // 0-1
  dependencies?: string[];
  alternatives?: string[];
}

export interface ReasoningChain {
  goal: string;
  steps: ThoughtStep[];
  current_step: number;
  confidence_score: number;
  reasoning_path: string[];
}

export interface CritiqueResult {
  is_valid: boolean;
  issues: string[];
  suggestions: string[];
  confidence_adjustment: number;
}

export class ChainOfThoughtReasoning {
  private reasoningChain: ReasoningChain;
  private critiques: CritiqueResult[] = [];

  constructor(goal: string) {
    this.reasoningChain = {
      goal,
      steps: [],
      current_step: 0,
      confidence_score: 0,
      reasoning_path: [],
    };

    if (isLoggingEnabled()) {
      log(`ChainOfThoughtReasoning initialized for goal: ${goal}`);
    }
  }

  // Break down a complex problem into logical steps
  async breakDownProblem(problem: string): Promise<ThoughtStep[]> {
    if (isLoggingEnabled()) {
      log(`Breaking down problem: ${problem}`);
    }

    const steps: ThoughtStep[] = [];

    // Step 1: Problem Analysis
    steps.push({
      id: "problem_analysis",
      type: "problem_analysis",
      description: "Analyze the problem and understand requirements",
      reasoning: `First, I need to understand what exactly needs to be done: ${problem}. This involves identifying the key components, constraints, and expected outcomes.`,
      confidence: 0.9,
    });

    // Step 2: Solution Planning
    steps.push({
      id: "solution_planning",
      type: "solution_planning",
      description: "Plan the solution approach",
      reasoning: "Based on the problem analysis, I need to devise a step-by-step approach that addresses all requirements while considering best practices and potential challenges.",
      confidence: 0.8,
      dependencies: ["problem_analysis"],
    });

    // Step 3: Implementation
    steps.push({
      id: "implementation",
      type: "implementation",
      description: "Implement the solution",
      reasoning: "Execute the planned solution, making adjustments as needed based on real-world constraints and feedback.",
      confidence: 0.7,
      dependencies: ["solution_planning"],
    });

    // Step 4: Validation
    steps.push({
      id: "validation",
      type: "validation",
      description: "Validate the solution works correctly",
      reasoning: "Test the implementation to ensure it meets all requirements and handles edge cases appropriately.",
      confidence: 0.8,
      dependencies: ["implementation"],
    });

    // Step 5: Reflection
    steps.push({
      id: "reflection",
      type: "reflection",
      description: "Reflect on the solution and identify improvements",
      reasoning: "Analyze the final solution for potential optimizations, maintainability improvements, and lessons learned.",
      confidence: 0.9,
      dependencies: ["validation"],
    });

    this.reasoningChain.steps = steps;
    this.updateConfidenceScore();

    return steps;
  }

  // Add a reasoning step
  addReasoningStep(step: ThoughtStep): void {
    this.reasoningChain.steps.push(step);
    this.reasoningChain.reasoning_path.push(step.reasoning);
    this.updateConfidenceScore();

    if (isLoggingEnabled()) {
      log(`Added reasoning step: ${step.id} (confidence: ${step.confidence})`);
    }
  }

  // Get the next logical step
  getNextStep(): ThoughtStep | null {
    if (this.reasoningChain.current_step >= this.reasoningChain.steps.length) {
      return null;
    }

    const nextStep = this.reasoningChain.steps[this.reasoningChain.current_step];
    
    // Check if dependencies are met
    if (nextStep.dependencies) {
      const completedSteps = this.reasoningChain.steps.slice(0, this.reasoningChain.current_step);
      const completedIds = completedSteps.map(s => s.id);
      
      const unmetDependencies = nextStep.dependencies.filter(dep => !completedIds.includes(dep));
      if (unmetDependencies.length > 0) {
        if (isLoggingEnabled()) {
          log(`Cannot proceed with step ${nextStep.id}: unmet dependencies ${unmetDependencies.join(", ")}`);
        }
        return null;
      }
    }

    return nextStep;
  }

  // Mark current step as completed and move to next
  completeCurrentStep(): void {
    if (this.reasoningChain.current_step < this.reasoningChain.steps.length) {
      this.reasoningChain.current_step++;
      
      if (isLoggingEnabled()) {
        log(`Completed step ${this.reasoningChain.current_step}/${this.reasoningChain.steps.length}`);
      }
    }
  }

  // Self-critique the reasoning chain
  async selfCritique(): Promise<CritiqueResult> {
    if (isLoggingEnabled()) {
      log("Performing self-critique of reasoning chain");
    }

    const issues: string[] = [];
    const suggestions: string[] = [];
    let confidenceAdjustment = 0;

    // Check for logical consistency
    for (let i = 0; i < this.reasoningChain.steps.length; i++) {
      const step = this.reasoningChain.steps[i];
      
      // Check if step has dependencies that come after it
      if (step.dependencies) {
        for (const dep of step.dependencies) {
          const depIndex = this.reasoningChain.steps.findIndex(s => s.id === dep);
          if (depIndex > i) {
            issues.push(`Step ${step.id} depends on ${dep} which comes later in the chain`);
            confidenceAdjustment -= 0.1;
          }
        }
      }

      // Check confidence levels
      if (step.confidence < 0.5) {
        issues.push(`Step ${step.id} has low confidence (${step.confidence})`);
        suggestions.push(`Consider breaking down step ${step.id} into smaller, more manageable parts`);
      }
    }

    // Check for missing critical steps
    const stepTypes = this.reasoningChain.steps.map(s => s.type);
    const criticalTypes = ["problem_analysis", "solution_planning", "implementation", "validation"];
    
    for (const criticalType of criticalTypes) {
      if (!stepTypes.includes(criticalType)) {
        issues.push(`Missing critical step type: ${criticalType}`);
        suggestions.push(`Add a ${criticalType} step to improve solution quality`);
        confidenceAdjustment -= 0.2;
      }
    }

    // Check reasoning quality
    const avgConfidence = this.reasoningChain.steps.reduce((sum, step) => sum + step.confidence, 0) / this.reasoningChain.steps.length;
    if (avgConfidence < 0.7) {
      issues.push("Overall confidence is low");
      suggestions.push("Review and strengthen the reasoning for each step");
      confidenceAdjustment -= 0.1;
    }

    const critique: CritiqueResult = {
      is_valid: issues.length === 0,
      issues,
      suggestions,
      confidence_adjustment: Math.max(-0.5, Math.min(0.2, confidenceAdjustment)),
    };

    this.critiques.push(critique);
    this.adjustConfidence(critique.confidence_adjustment);

    if (isLoggingEnabled()) {
      log(`Self-critique completed: ${issues.length} issues found, confidence adjusted by ${critique.confidence_adjustment}`);
    }

    return critique;
  }

  // Generate alternative approaches
  generateAlternatives(stepId: string): string[] {
    const step = this.reasoningChain.steps.find(s => s.id === stepId);
    if (!step) {
      return [];
    }

    const alternatives: string[] = [];

    switch (step.type) {
      case "problem_analysis":
        alternatives.push(
          "Use a different problem decomposition approach",
          "Apply domain-specific analysis techniques",
          "Consult external resources for similar problems"
        );
        break;
      
      case "solution_planning":
        alternatives.push(
          "Consider a more iterative approach",
          "Explore different architectural patterns",
          "Evaluate trade-offs between different solutions"
        );
        break;
      
      case "implementation":
        alternatives.push(
          "Use a different programming paradigm",
          "Implement in smaller increments",
          "Apply different design patterns"
        );
        break;
      
      case "validation":
        alternatives.push(
          "Use different testing strategies",
          "Implement more comprehensive error handling",
          "Add performance benchmarking"
        );
        break;
      
      case "reflection":
        alternatives.push(
          "Conduct a more thorough code review",
          "Analyze long-term maintainability",
          "Consider scalability implications"
        );
        break;
    }

    if (isLoggingEnabled()) {
      log(`Generated ${alternatives.length} alternatives for step ${stepId}`);
    }

    return alternatives;
  }

  // Update overall confidence score
  private updateConfidenceScore(): void {
    if (this.reasoningChain.steps.length === 0) {
      this.reasoningChain.confidence_score = 0;
      return;
    }

    const avgConfidence = this.reasoningChain.steps.reduce((sum, step) => sum + step.confidence, 0) / this.reasoningChain.steps.length;
    
    // Apply penalty for incomplete reasoning chains
    const completionFactor = this.reasoningChain.steps.length >= 5 ? 1 : this.reasoningChain.steps.length / 5;
    
    this.reasoningChain.confidence_score = avgConfidence * completionFactor;
  }

  // Adjust confidence based on critique
  private adjustConfidence(adjustment: number): void {
    this.reasoningChain.confidence_score = Math.max(0, Math.min(1, this.reasoningChain.confidence_score + adjustment));
  }

  // Get the current reasoning chain
  getReasoningChain(): ReasoningChain {
    return { ...this.reasoningChain };
  }

  // Get all critiques
  getCritiques(): CritiqueResult[] {
    return [...this.critiques];
  }

  // Generate a summary of the reasoning process
  generateSummary(): string {
    const chain = this.reasoningChain;
    const summary = [
      `Goal: ${chain.goal}`,
      `Steps: ${chain.steps.length}`,
      `Current Step: ${chain.current_step}/${chain.steps.length}`,
      `Overall Confidence: ${(chain.confidence_score * 100).toFixed(1)}%`,
      "",
      "Reasoning Path:",
      ...chain.reasoning_path.map((reasoning, index) => `${index + 1}. ${reasoning}`),
    ];

    if (this.critiques.length > 0) {
      summary.push("", "Critiques:");
      this.critiques.forEach((critique, index) => {
        summary.push(`Critique ${index + 1}: ${critique.is_valid ? "Valid" : "Issues found"}`);
        if (critique.issues.length > 0) {
          summary.push(`  Issues: ${critique.issues.join(", ")}`);
        }
        if (critique.suggestions.length > 0) {
          summary.push(`  Suggestions: ${critique.suggestions.join(", ")}`);
        }
      });
    }

    return summary.join("\n");
  }

  // Reset the reasoning chain
  reset(): void {
    this.reasoningChain.steps = [];
    this.reasoningChain.current_step = 0;
    this.reasoningChain.confidence_score = 0;
    this.reasoningChain.reasoning_path = [];
    this.critiques = [];

    if (isLoggingEnabled()) {
      log("Chain-of-thought reasoning reset");
    }
  }
}
