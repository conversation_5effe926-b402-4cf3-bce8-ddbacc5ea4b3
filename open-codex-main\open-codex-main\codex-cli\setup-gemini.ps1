# Setup script for Open-Codex with Gemini API
Write-Host "🚀 Setting up Open-Codex with Gemini API..." -ForegroundColor Green

# Set environment variable for current session
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"
Write-Host "✓ Environment variable set for current session" -ForegroundColor Green

# Create .codex directory in user home
$codexDir = Join-Path $env:USERPROFILE ".codex"
if (!(Test-Path $codexDir)) {
    New-Item -ItemType Directory -Path $codexDir -Force | Out-Null
    Write-Host "✓ Created .codex directory: $codexDir" -ForegroundColor Green
} else {
    Write-Host "✓ .codex directory already exists: $codexDir" -ForegroundColor Green
}

# Create config.json with Gemini settings
$configPath = Join-Path $codexDir "config.json"
$configContent = @{
    provider = "gemini"
    model = "gemini-2.0-flash"
    fullAutoErrorMode = "ask-user"
} | ConvertTo-Json -Depth 3

Set-Content -Path $configPath -Value $configContent -Encoding UTF8
Write-Host "✓ Created config file: $configPath" -ForegroundColor Green

# Display config content
Write-Host "`n📋 Configuration:" -ForegroundColor Cyan
Get-Content $configPath | Write-Host

# Test the CLI
Write-Host "`n🧪 Testing CLI..." -ForegroundColor Cyan
try {
    & node dist/cli.js --provider gemini --help
    Write-Host "✓ CLI test successful!" -ForegroundColor Green
} catch {
    Write-Host "❌ CLI test failed: $_" -ForegroundColor Red
}

Write-Host "`n🎉 Setup complete!" -ForegroundColor Green
Write-Host "You can now use: open-codex --provider gemini 'your prompt here'" -ForegroundColor Yellow
Write-Host "Or simply: node dist/cli.js --provider gemini 'your prompt here'" -ForegroundColor Yellow
