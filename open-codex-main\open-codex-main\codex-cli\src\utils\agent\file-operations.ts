import * as fs from "fs";
import * as path from "path";
import { promisify } from "util";
import { glob } from "glob";
import { isLoggingEnabled, log } from "./log.js";

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);
const rmdir = promisify(fs.rmdir);
const copyFile = promisify(fs.copyFile);
const rename = promisify(fs.rename);

export interface FileOperationArgs {
  operation: "create" | "read" | "write" | "delete" | "search" | "list" | "mkdir" | "copy" | "move" | "grep";
  path: string;
  content?: string;
  pattern?: string;
  recursive?: boolean;
  destination?: string;
}

export interface FileOperationResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    operation: string;
    path: string;
    size?: number;
    modified?: Date;
    type?: string;
  };
}

export class FileOperationsHandler {
  private activeFiles: Set<string> = new Set();
  private workingDirectory: string = process.cwd();

  constructor() {
    if (isLoggingEnabled()) {
      log("FileOperationsHandler initialized");
    }
  }

  async handleFileOperation(args: FileOperationArgs): Promise<FileOperationResult> {
    try {
      const resolvedPath = path.resolve(this.workingDirectory, args.path);
      
      if (isLoggingEnabled()) {
        log(`File operation: ${args.operation} on ${resolvedPath}`);
      }

      switch (args.operation) {
        case "create":
          return await this.createFile(resolvedPath, args.content || "");
        case "read":
          return await this.readFile(resolvedPath);
        case "write":
          return await this.writeFile(resolvedPath, args.content || "");
        case "delete":
          return await this.deleteFile(resolvedPath);
        case "search":
          return await this.searchFiles(resolvedPath, args.pattern || "", args.recursive);
        case "list":
          return await this.listDirectory(resolvedPath, args.recursive);
        case "mkdir":
          return await this.createDirectory(resolvedPath, args.recursive);
        case "copy":
          return await this.copyFile(resolvedPath, args.destination!);
        case "move":
          return await this.moveFile(resolvedPath, args.destination!);
        case "grep":
          return await this.grepInFiles(resolvedPath, args.pattern || "", args.recursive);
        default:
          throw new Error(`Unknown operation: ${args.operation}`);
      }
    } catch (error) {
      if (isLoggingEnabled()) {
        log(`File operation error: ${error}`);
      }
      return {
        success: false,
        error: String(error),
        metadata: {
          operation: args.operation,
          path: args.path,
        },
      };
    }
  }

  private async createFile(filePath: string, content: string): Promise<FileOperationResult> {
    // Ensure directory exists
    const dir = path.dirname(filePath);
    await mkdir(dir, { recursive: true });
    
    await writeFile(filePath, content, "utf8");
    this.activeFiles.add(filePath);
    
    const stats = await stat(filePath);
    return {
      success: true,
      data: `File created: ${filePath}`,
      metadata: {
        operation: "create",
        path: filePath,
        size: stats.size,
        modified: stats.mtime,
        type: "file",
      },
    };
  }

  private async readFile(filePath: string): Promise<FileOperationResult> {
    const content = await readFile(filePath, "utf8");
    this.activeFiles.add(filePath);
    
    const stats = await stat(filePath);
    return {
      success: true,
      data: content,
      metadata: {
        operation: "read",
        path: filePath,
        size: stats.size,
        modified: stats.mtime,
        type: "file",
      },
    };
  }

  private async writeFile(filePath: string, content: string): Promise<FileOperationResult> {
    // Ensure directory exists
    const dir = path.dirname(filePath);
    await mkdir(dir, { recursive: true });
    
    await writeFile(filePath, content, "utf8");
    this.activeFiles.add(filePath);
    
    const stats = await stat(filePath);
    return {
      success: true,
      data: `File written: ${filePath}`,
      metadata: {
        operation: "write",
        path: filePath,
        size: stats.size,
        modified: stats.mtime,
        type: "file",
      },
    };
  }

  private async deleteFile(filePath: string): Promise<FileOperationResult> {
    const stats = await stat(filePath);
    
    if (stats.isDirectory()) {
      await rmdir(filePath, { recursive: true });
    } else {
      await unlink(filePath);
    }
    
    this.activeFiles.delete(filePath);
    
    return {
      success: true,
      data: `Deleted: ${filePath}`,
      metadata: {
        operation: "delete",
        path: filePath,
        type: stats.isDirectory() ? "directory" : "file",
      },
    };
  }

  private async searchFiles(searchPath: string, pattern: string, recursive?: boolean): Promise<FileOperationResult> {
    const globPattern = recursive 
      ? path.join(searchPath, "**", `*${pattern}*`)
      : path.join(searchPath, `*${pattern}*`);
    
    const files = await glob(globPattern);
    
    return {
      success: true,
      data: files,
      metadata: {
        operation: "search",
        path: searchPath,
        type: "search_results",
      },
    };
  }

  private async listDirectory(dirPath: string, recursive?: boolean): Promise<FileOperationResult> {
    if (recursive) {
      const files = await glob(path.join(dirPath, "**", "*"));
      const fileDetails = await Promise.all(
        files.map(async (file) => {
          const stats = await stat(file);
          return {
            path: file,
            type: stats.isDirectory() ? "directory" : "file",
            size: stats.size,
            modified: stats.mtime,
          };
        })
      );
      
      return {
        success: true,
        data: fileDetails,
        metadata: {
          operation: "list",
          path: dirPath,
          type: "directory_listing",
        },
      };
    } else {
      const files = await readdir(dirPath);
      const fileDetails = await Promise.all(
        files.map(async (file) => {
          const fullPath = path.join(dirPath, file);
          const stats = await stat(fullPath);
          return {
            name: file,
            path: fullPath,
            type: stats.isDirectory() ? "directory" : "file",
            size: stats.size,
            modified: stats.mtime,
          };
        })
      );
      
      return {
        success: true,
        data: fileDetails,
        metadata: {
          operation: "list",
          path: dirPath,
          type: "directory_listing",
        },
      };
    }
  }

  private async createDirectory(dirPath: string, recursive?: boolean): Promise<FileOperationResult> {
    await mkdir(dirPath, { recursive });
    
    return {
      success: true,
      data: `Directory created: ${dirPath}`,
      metadata: {
        operation: "mkdir",
        path: dirPath,
        type: "directory",
      },
    };
  }

  private async copyFile(sourcePath: string, destPath: string): Promise<FileOperationResult> {
    // Ensure destination directory exists
    const destDir = path.dirname(destPath);
    await mkdir(destDir, { recursive: true });
    
    await copyFile(sourcePath, destPath);
    
    const stats = await stat(destPath);
    return {
      success: true,
      data: `File copied from ${sourcePath} to ${destPath}`,
      metadata: {
        operation: "copy",
        path: destPath,
        size: stats.size,
        modified: stats.mtime,
        type: "file",
      },
    };
  }

  private async moveFile(sourcePath: string, destPath: string): Promise<FileOperationResult> {
    // Ensure destination directory exists
    const destDir = path.dirname(destPath);
    await mkdir(destDir, { recursive: true });
    
    await rename(sourcePath, destPath);
    this.activeFiles.delete(sourcePath);
    this.activeFiles.add(destPath);
    
    const stats = await stat(destPath);
    return {
      success: true,
      data: `File moved from ${sourcePath} to ${destPath}`,
      metadata: {
        operation: "move",
        path: destPath,
        size: stats.size,
        modified: stats.mtime,
        type: "file",
      },
    };
  }

  private async grepInFiles(searchPath: string, pattern: string, recursive?: boolean): Promise<FileOperationResult> {
    const globPattern = recursive 
      ? path.join(searchPath, "**", "*")
      : path.join(searchPath, "*");
    
    const files = await glob(globPattern);
    const results: Array<{ file: string; line: number; content: string }> = [];
    
    const regex = new RegExp(pattern, "gi");
    
    for (const file of files) {
      try {
        const stats = await stat(file);
        if (stats.isFile()) {
          const content = await readFile(file, "utf8");
          const lines = content.split("\n");
          
          lines.forEach((line, index) => {
            if (regex.test(line)) {
              results.push({
                file,
                line: index + 1,
                content: line.trim(),
              });
            }
          });
        }
      } catch (error) {
        // Skip files that can't be read
        continue;
      }
    }
    
    return {
      success: true,
      data: results,
      metadata: {
        operation: "grep",
        path: searchPath,
        type: "grep_results",
      },
    };
  }

  getActiveFiles(): string[] {
    return Array.from(this.activeFiles);
  }

  setWorkingDirectory(dir: string): void {
    this.workingDirectory = path.resolve(dir);
    if (isLoggingEnabled()) {
      log(`Working directory changed to: ${this.workingDirectory}`);
    }
  }

  getWorkingDirectory(): string {
    return this.workingDirectory;
  }
}
