{"version": 3, "file": "non-nullable-type-assertion-style.js", "sourceRoot": "", "sources": ["../../src/rules/non-nullable-type-assertion-style.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAKiB;AAEjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,mCAAmC;IACzC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,sDAAsD;YACnE,WAAW,EAAE,WAAW;YACxB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,sBAAsB,EACpB,+EAA+E;SAClF;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAElB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAE5C,MAAM,kBAAkB,GAAG,CAAC,IAAmB,EAAyB,EAAE;YACxE,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE9C,IACE,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EACpE,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;QACtC,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CAAC,IAAa,EAAW,EAAE;YAChD,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,EAAE,CAAC;gBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;gBACxC,OAAO,UAAU,IAAI,IAAI,IAAI,cAAc,CAAC,UAAU,CAAC,CAAC;YAC1D,CAAC;iBAAM,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBACrC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBAC9B,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;wBACzB,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC;QAC3E,CAAC,CAAC;QAEF,MAAM,sBAAsB,GAAG,CAC7B,aAAwB,EACxB,aAAwB,EACf,EAAE;YACX,MAAM,uBAAuB,GAAG,aAAa,CAAC,MAAM,CAClD,IAAI,CAAC,EAAE,CACL,CAAC,IAAI,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CACpE,CAAC;YAEF,IAAI,uBAAuB,CAAC,MAAM,KAAK,aAAa,CAAC,MAAM,EAAE,CAAC;gBAC5D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACzC,IACE,cAAc,CAAC,YAAY,CAAC;oBAC5B,CAAC,uBAAuB,CAAC,QAAQ,CAAC,YAAY,CAAC,EAC/C,CAAC;oBACD,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,KAAK,MAAM,YAAY,IAAI,uBAAuB,EAAE,CAAC;gBACnD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC1C,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC;QAEF,MAAM,gBAAgB,GAAG,CACvB,IAAwD,EAC/C,EAAE;YACX,OAAO,CACL,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC3D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC/D,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC9C,CAAC;QACJ,CAAC,CAAC;QAEF,OAAO;YACL,iCAAiC,CAC/B,IAAwD;gBAExD,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC3B,OAAO;gBACT,CAAC;gBAED,MAAM,aAAa,GAAG,kBAAkB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAC1D,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO;gBACT,CAAC;gBAED,MAAM,aAAa,GAAG,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC9D,IAAI,CAAC,aAAa,EAAE,CAAC;oBACnB,OAAO;gBACT,CAAC;gBAED,IAAI,sBAAsB,CAAC,aAAa,EAAE,aAAa,CAAC,EAAE,CAAC;oBACzD,MAAM,oBAAoB,GAAG,OAAO,CAAC,UAAU,CAAC,OAAO,CACrD,IAAI,CAAC,UAAU,CAChB,CAAC;oBAEF,MAAM,yBAAyB,GAC7B,IAAA,4BAAqB,EACnB,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,IAAI,EACxD,EAAE,CAAC,UAAU,CAAC,OAAO,CACtB,GAAG,yBAAkB,CAAC,KAAK,CAAC;oBAE/B,OAAO,CAAC,MAAM,CAAC;wBACb,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CACtB,IAAI,EACJ,yBAAyB;gCACvB,CAAC,CAAC,GAAG,oBAAoB,GAAG;gCAC5B,CAAC,CAAC,IAAI,oBAAoB,IAAI,CACjC,CAAC;wBACJ,CAAC;wBACD,SAAS,EAAE,wBAAwB;wBACnC,IAAI;qBACL,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}