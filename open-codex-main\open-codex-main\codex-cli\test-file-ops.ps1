# Test file operations functionality
Write-Host "🧪 TESTING FILE OPERATIONS" -ForegroundColor Cyan
Write-Host "===========================" -ForegroundColor Cyan

# Set environment variable
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"

# Test 1: Simple file creation request
Write-Host "`n📝 TEST 1: File Creation Request" -ForegroundColor Yellow
Write-Host "Requesting AI to create a simple text file..." -ForegroundColor Gray

try {
    $result = & node dist/cli.js --provider gemini --dangerously-auto-approve-everything --quiet "Create a file called test.txt with the content 'Hello World'" 2>&1
    Write-Host "Command completed" -ForegroundColor Green
    Write-Host "Result: $result" -ForegroundColor Gray
    
    # Check if file was created
    if (Test-Path "test.txt") {
        Write-Host "✅ File created successfully!" -ForegroundColor Green
        $content = Get-Content "test.txt" -Raw
        Write-Host "File content: $content" -ForegroundColor Gray
        Remove-Item "test.txt" -Force
    } else {
        Write-Host "❌ File was not created" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Test failed: $_" -ForegroundColor Red
}

# Test 2: HTML file creation
Write-Host "`n🌐 TEST 2: HTML File Creation" -ForegroundColor Yellow
Write-Host "Requesting AI to create an HTML file..." -ForegroundColor Gray

try {
    $result = & node dist/cli.js --provider gemini --dangerously-auto-approve-everything --quiet "Create an index.html file with basic HTML structure including head, title, and body with hello world" 2>&1
    Write-Host "Command completed" -ForegroundColor Green
    Write-Host "Result: $result" -ForegroundColor Gray
    
    # Check if file was created
    if (Test-Path "index.html") {
        Write-Host "✅ HTML file created successfully!" -ForegroundColor Green
        $content = Get-Content "index.html" -Raw
        Write-Host "File content preview: $($content.Substring(0, [Math]::Min(200, $content.Length)))..." -ForegroundColor Gray
    } else {
        Write-Host "❌ HTML file was not created" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ HTML test failed: $_" -ForegroundColor Red
}

Write-Host "`n🏁 FILE OPERATIONS TEST COMPLETE" -ForegroundColor Cyan
