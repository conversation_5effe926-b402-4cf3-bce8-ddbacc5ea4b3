import { isLoggingEnabled, log } from "./log.js";

export interface WorkflowStep {
  id: string;
  type: "execute" | "analyze" | "plan" | "validate";
  description: string;
  action: () => Promise<WorkflowStepResult>;
  dependencies?: string[];
  timeout?: number;
  retries?: number;
}

export interface WorkflowStepResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    step_id: string;
    execution_time: number;
    retry_count?: number;
  };
}

export interface WorkflowContext {
  goal: string;
  current_step: number;
  total_steps: number;
  active_files: string[];
  working_directory: string;
  variables: Record<string, any>;
  history: WorkflowStepResult[];
}

export class WorkflowEngine {
  private context: WorkflowContext;
  private steps: WorkflowStep[] = [];
  private isRunning: boolean = false;
  private abortController: AbortController | null = null;

  constructor(goal: string, workingDirectory: string = process.cwd()) {
    this.context = {
      goal,
      current_step: 0,
      total_steps: 0,
      active_files: [],
      working_directory: workingDirectory,
      variables: {},
      history: [],
    };

    if (isLoggingEnabled()) {
      log(`WorkflowEngine initialized with goal: ${goal}`);
    }
  }

  // Add a step to the workflow
  addStep(step: WorkflowStep): void {
    this.steps.push(step);
    this.context.total_steps = this.steps.length;
    
    if (isLoggingEnabled()) {
      log(`Added workflow step: ${step.id} (${step.type})`);
    }
  }

  // Execute the workflow step by step
  async executeWorkflow(): Promise<WorkflowContext> {
    if (this.isRunning) {
      throw new Error("Workflow is already running");
    }

    this.isRunning = true;
    this.abortController = new AbortController();
    
    if (isLoggingEnabled()) {
      log(`Starting workflow execution: ${this.context.goal}`);
    }

    try {
      for (let i = 0; i < this.steps.length; i++) {
        if (this.abortController.signal.aborted) {
          throw new Error("Workflow aborted");
        }

        this.context.current_step = i + 1;
        const step = this.steps[i];
        
        if (isLoggingEnabled()) {
          log(`Executing step ${i + 1}/${this.steps.length}: ${step.id}`);
        }

        // Check dependencies
        if (step.dependencies) {
          const unmetDependencies = this.checkDependencies(step.dependencies);
          if (unmetDependencies.length > 0) {
            throw new Error(`Unmet dependencies for step ${step.id}: ${unmetDependencies.join(", ")}`);
          }
        }

        const result = await this.executeStepWithRetry(step);
        this.context.history.push(result);

        if (!result.success) {
          if (isLoggingEnabled()) {
            log(`Step ${step.id} failed: ${result.error}`);
          }
          
          // Analyze the failure and decide whether to continue or abort
          const shouldContinue = await this.analyzeFailure(step, result);
          if (!shouldContinue) {
            throw new Error(`Workflow stopped due to critical failure in step: ${step.id}`);
          }
        }

        // Analyze results and plan next steps
        await this.analyzeStepResult(step, result);
      }

      if (isLoggingEnabled()) {
        log("Workflow execution completed successfully");
      }

      return this.context;
    } catch (error) {
      if (isLoggingEnabled()) {
        log(`Workflow execution failed: ${error}`);
      }
      throw error;
    } finally {
      this.isRunning = false;
      this.abortController = null;
    }
  }

  // Execute a single step with retry logic
  private async executeStepWithRetry(step: WorkflowStep): Promise<WorkflowStepResult> {
    const maxRetries = step.retries || 0;
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const startTime = Date.now();
        
        // Set up timeout if specified
        let timeoutId: NodeJS.Timeout | null = null;
        const timeoutPromise = step.timeout ? new Promise<never>((_, reject) => {
          timeoutId = setTimeout(() => {
            reject(new Error(`Step ${step.id} timed out after ${step.timeout}ms`));
          }, step.timeout);
        }) : null;

        // Execute the step
        const resultPromise = step.action();
        const result = timeoutPromise 
          ? await Promise.race([resultPromise, timeoutPromise])
          : await resultPromise;

        if (timeoutId) {
          clearTimeout(timeoutId);
        }

        const executionTime = Date.now() - startTime;
        
        return {
          ...result,
          metadata: {
            ...result.metadata,
            step_id: step.id,
            execution_time: executionTime,
            retry_count: attempt,
          },
        };
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < maxRetries) {
          if (isLoggingEnabled()) {
            log(`Step ${step.id} failed (attempt ${attempt + 1}/${maxRetries + 1}): ${error}. Retrying...`);
          }
          
          // Wait before retrying (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // All retries exhausted
    return {
      success: false,
      error: lastError?.message || "Unknown error",
      metadata: {
        step_id: step.id,
        execution_time: 0,
        retry_count: maxRetries,
      },
    };
  }

  // Check if step dependencies are met
  private checkDependencies(dependencies: string[]): string[] {
    const unmetDependencies: string[] = [];
    
    for (const dep of dependencies) {
      const depStep = this.context.history.find(h => h.metadata?.step_id === dep);
      if (!depStep || !depStep.success) {
        unmetDependencies.push(dep);
      }
    }
    
    return unmetDependencies;
  }

  // Analyze step failure and decide whether to continue
  private async analyzeFailure(step: WorkflowStep, result: WorkflowStepResult): Promise<boolean> {
    // For now, simple logic: continue for non-critical steps, abort for critical ones
    const criticalStepTypes = ["validate"];
    
    if (criticalStepTypes.includes(step.type)) {
      return false; // Abort workflow
    }
    
    // For other step types, continue but log the issue
    if (isLoggingEnabled()) {
      log(`Non-critical step ${step.id} failed, continuing workflow`);
    }
    
    return true; // Continue workflow
  }

  // Analyze step result and update context
  private async analyzeStepResult(step: WorkflowStep, result: WorkflowStepResult): Promise<void> {
    // Update context based on step result
    if (result.success && result.data) {
      // Extract useful information from the result
      if (step.type === "execute" && result.data.active_files) {
        this.context.active_files = [...new Set([...this.context.active_files, ...result.data.active_files])];
      }
      
      if (result.data.variables) {
        this.context.variables = { ...this.context.variables, ...result.data.variables };
      }
    }

    if (isLoggingEnabled()) {
      log(`Step ${step.id} analysis complete. Context updated.`);
    }
  }

  // Abort the workflow
  abort(): void {
    if (this.abortController) {
      this.abortController.abort();
      if (isLoggingEnabled()) {
        log("Workflow aborted by user");
      }
    }
  }

  // Get current workflow status
  getStatus(): {
    isRunning: boolean;
    currentStep: number;
    totalSteps: number;
    progress: number;
    goal: string;
  } {
    return {
      isRunning: this.isRunning,
      currentStep: this.context.current_step,
      totalSteps: this.context.total_steps,
      progress: this.context.total_steps > 0 ? (this.context.current_step / this.context.total_steps) * 100 : 0,
      goal: this.context.goal,
    };
  }

  // Get workflow context
  getContext(): WorkflowContext {
    return { ...this.context };
  }

  // Add a file to active files
  addActiveFile(filePath: string): void {
    if (!this.context.active_files.includes(filePath)) {
      this.context.active_files.push(filePath);
      if (isLoggingEnabled()) {
        log(`Added active file: ${filePath}`);
      }
    }
  }

  // Remove a file from active files
  removeActiveFile(filePath: string): void {
    const index = this.context.active_files.indexOf(filePath);
    if (index > -1) {
      this.context.active_files.splice(index, 1);
      if (isLoggingEnabled()) {
        log(`Removed active file: ${filePath}`);
      }
    }
  }

  // Set a context variable
  setVariable(key: string, value: any): void {
    this.context.variables[key] = value;
    if (isLoggingEnabled()) {
      log(`Set context variable: ${key} = ${JSON.stringify(value)}`);
    }
  }

  // Get a context variable
  getVariable(key: string): any {
    return this.context.variables[key];
  }

  // Clear workflow state
  reset(): void {
    this.context.current_step = 0;
    this.context.history = [];
    this.context.active_files = [];
    this.context.variables = {};
    this.steps = [];
    this.context.total_steps = 0;
    
    if (isLoggingEnabled()) {
      log("Workflow engine reset");
    }
  }
}
