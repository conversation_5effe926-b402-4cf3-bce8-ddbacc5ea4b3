{"version": 3, "file": "no-type-alias.js", "sourceRoot": "", "sources": ["../../src/rules/no-type-alias.ts"], "names": [], "mappings": ";;AACA,oDAA2E;AAE3E,kCAAqC;AA+BrC,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,uBAAuB;YACpC,oCAAoC;SACrC;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,iCAAiC;YAC9C,kBAAkB,EAChB,4DAA4D;SAC/D;QACD,MAAM,EAAE;YACN;gBACE,KAAK,EAAE;oBACL,eAAe,EAAE;wBACf,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAW;4BACX,kBAAkB;4BAClB,6BAA6B;yBACX;qBACrB;oBACD,aAAa,EAAE;wBACb,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;iBACF;gBACD,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,YAAY,EAAE;wBACZ,WAAW,EAAE,kDAAkD;wBAC/D,IAAI,EAAE,iCAAiC;qBACxC;oBACD,cAAc,EAAE;wBACd,WAAW,EAAE,8CAA8C;wBAC3D,IAAI,EAAE,+BAA+B;qBACtC;oBACD,qBAAqB,EAAE;wBACrB,WAAW,EAAE,sDAAsD;wBACnE,IAAI,EAAE,+BAA+B;qBACtC;oBACD,iBAAiB,EAAE;wBACjB,WAAW,EAAE,kDAAkD;wBAC/D,IAAI,EAAE,+BAA+B;qBACtC;oBACD,aAAa,EAAE;wBACb,WAAW,EACT,0DAA0D;wBAC5D,IAAI,EAAE,iCAAiC;qBACxC;oBACD,gBAAgB,EAAE;wBAChB,WAAW,EAAE,kDAAkD;wBAC/D,IAAI,EAAE,iCAAiC;qBACxC;oBACD,eAAe,EAAE;wBACf,WAAW,EAAE,iDAAiD;wBAC9D,IAAI,EAAE,iCAAiC;qBACxC;oBACD,aAAa,EAAE;wBACb,WAAW,EAAE,mDAAmD;wBAChE,IAAI,EAAE,+BAA+B;qBACtC;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,YAAY,EAAE,OAAO;YACrB,cAAc,EAAE,OAAO;YACvB,qBAAqB,EAAE,OAAO;YAC9B,iBAAiB,EAAE,OAAO;YAC1B,aAAa,EAAE,OAAO;YACtB,gBAAgB,EAAE,OAAO;YACzB,eAAe,EAAE,OAAO;YACxB,aAAa,EAAE,OAAO;SACvB;KACF;IACD,MAAM,CACJ,OAAO,EACP,CACE,EACE,YAAY,EACZ,cAAc,EACd,qBAAqB,EACrB,iBAAiB,EACjB,aAAa,EACb,gBAAgB,EAChB,eAAe,EACf,aAAa,GACd,EACF;QAED,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,6BAA6B,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG;YACpB,QAAQ;YACR,kBAAkB;YAClB,6BAA6B;SAC9B,CAAC;QACF,MAAM,YAAY,GAAG;YACnB,WAAW;YACX,kBAAkB;YAClB,6BAA6B;SAC9B,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;YACzB,sBAAc,CAAC,WAAW;YAC1B,sBAAc,CAAC,YAAY;YAC3B,sBAAc,CAAC,eAAe;YAC9B,sBAAc,CAAC,aAAa;YAC5B,sBAAc,CAAC,WAAW;YAC1B,sBAAc,CAAC,mBAAmB;YAClC,sBAAc,CAAC,qBAAqB;SACrC,CAAC,CAAC;QAEH;;;;;WAKG;QACH,SAAS,sBAAsB,CAC7B,UAAmB,EACnB,eAAuC,EACvC,OAAe;YAEf,OAAO,CACL,CAAC,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC;gBAC/B,CAAC,CAAC,UAAU;oBACV,CAAC,CAAC,eAAe,KAAK,sBAAc,CAAC,WAAW;wBAC9C,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;wBACzB,CAAC,eAAe,KAAK,sBAAc,CAAC,kBAAkB;4BACpD,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CACzC,CAAC;QACJ,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,WAAW,CAClB,IAAmB,EACnB,eAAuC,EACvC,MAAe,EACf,IAAY;YAEZ,IAAI,MAAM,EAAE,CAAC;gBACX,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI;oBACJ,SAAS,EAAE,aAAa;oBACxB,IAAI,EAAE;wBACJ,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE;qBAC1B;iBACF,CAAC,CAAC;YACL,CAAC;YAED,OAAO,OAAO,CAAC,MAAM,CAAC;gBACpB,IAAI;gBACJ,SAAS,EAAE,oBAAoB;gBAC/B,IAAI,EAAE;oBACJ,eAAe,EACb,eAAe,KAAK,sBAAc,CAAC,WAAW;wBAC5C,CAAC,CAAC,OAAO;wBACT,CAAC,CAAC,cAAc;oBACpB,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;QACL,CAAC;QAED,MAAM,gBAAgB,GAAG,CAAC,IAAmB,EAAW,EAAE;YACxD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAAE,CAAC;gBAClD,OAAO,IAAI,CAAC;YACd,CAAC;YACD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;gBACrD,IACE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAClD,IAAI,CAAC,IAAI,CAAC,cAAc;oBACxB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,EAC5D,CAAC;oBACD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CAAC,IAAmB,EAAW,EAAE;YACtD,OAAO,CACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBACjD,IAAI,CAAC,IAAI,CAAC,aAAa,KAAK,SAAS,CACtC,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,CACrB,WAAmB,EACnB,UAAmB,EACnB,IAAmB,EACnB,KAAa,EACP,EAAE;YACR,IACE,WAAW,KAAK,OAAO;gBACvB,CAAC,sBAAsB,CAAC,UAAU,EAAE,IAAI,CAAC,eAAe,EAAE,WAAW,CAAC,EACtE,CAAC;gBACD,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;YAClE,CAAC;QACH,CAAC,CAAC;QAEF;;;;;WAKG;QACH,SAAS,mBAAmB,CAC1B,IAAmB,EACnB,UAAU,GAAG,KAAK;YAElB,qEAAqE;YACrE,6DAA6D;YAC7D,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;gBACrD,WAAW;gBACX,IAAI,cAAc,KAAK,OAAO,EAAE,CAAC;oBAC/B,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;gBACxE,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBAC/D,mBAAmB;gBACnB,IAAI,qBAAqB,KAAK,OAAO,EAAE,CAAC;oBACtC,WAAW,CACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,eAAe,EACpB,UAAU,EACV,mBAAmB,CACpB,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB,EAAE,CAAC;gBAC/D,IAAI,iBAAiB,KAAK,OAAO,EAAE,CAAC;oBAClC,WAAW,CACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,eAAe,EACpB,UAAU,EACV,cAAc,CACf,CAAC;gBACJ,CAAC;YACH,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE,CAAC;gBAC3D,sBAAsB;gBACtB,cAAc,CAAC,aAAc,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;YAC/D,CAAC;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,YAAY,EAAE,CAAC;gBAC1D,cAAc;gBACd,cAAc,CAAC,gBAAiB,EAAE,UAAU,EAAE,IAAI,EAAE,cAAc,CAAC,CAAC;YACtE,CAAC;iBAAM,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;gBAClC,cAAc;gBACd,cAAc,CAAC,eAAgB,EAAE,UAAU,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;YACpE,CAAC;iBAAM,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC;gBAChC,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;oBAC9B,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;gBACvE,CAAC;YACH,CAAC;iBAAM,IACL,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAe,CAAC,OAAO,CAAC;gBAChD,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC9B,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;oBAC/C,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,OAAO;wBAC7B,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,KAAK,UAAU;4BAChC,IAAI,CAAC,IAAI,CAAC,cAAc;4BACxB,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EACtD,CAAC;gBACD,kBAAkB;gBAClB,cAAc,CAAC,YAAa,EAAE,UAAU,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAC7D,CAAC;iBAAM,CAAC;gBACN,oCAAoC;gBACpC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;YACxE,CAAC;YACD,4DAA4D;QAC9D,CAAC;QAED;;WAEG;QACH,SAAS,QAAQ,CACf,IAAmB,EACnB,kBAA0C,IAAI;YAE9C,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW;gBACxC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EAC/C,CAAC;gBACD,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;oBACtD,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvC,OAAO,GAAG,CAAC;gBACb,CAAC,EAAE,EAAE,CAAC,CAAC;YACT,CAAC;YACD,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,OAAO;YACL,sBAAsB,CAAC,IAAI;gBACzB,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC5C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACvB,iCAAiC;oBACjC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;gBACtC,CAAC;qBAAM,CAAC;oBACN,wBAAwB;oBACxB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACnB,mBAAmB,CAAC,IAAI,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}