{
  "compilerOptions": {
    "outDir": "dist",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "target": "esnext",
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ES2022" // Node.js 18
    ],
    "types": ["node"],
    "baseUrl": "./",
    "resolveJsonModule": false, // ESM doesn't yet support JSON modules.
    "jsx": "react",
    "declaration": true,
    "newLine": "lf",
    "stripInternal": true,
    "strict": true,
    "noImplicitReturns": true,
    "noImplicitOverride": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    "noUncheckedIndexedAccess": true,
    "noPropertyAccessFromIndexSignature": true,
    "noUncheckedSideEffectImports": true,
    "noEmitOnError": true,
    "useDefineForClassFields": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true
  },
  "include": ["src", "tests"]
}
