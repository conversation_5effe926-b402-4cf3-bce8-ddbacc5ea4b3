# Enhanced AI Development Agent PowerShell Launcher
param(
    [Parameter(Position=0, ValueFromRemainingArguments=$true)]
    [string[]]$Arguments
)

# Set environment variable
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"

if ($Arguments.Count -eq 0) {
    Write-Host "🚀 Enhanced AI Development Agent" -ForegroundColor Cyan
    Write-Host "================================" -ForegroundColor Cyan
    Write-Host "Usage: .\codex.ps1 'your prompt here'" -ForegroundColor White
    Write-Host ""
    Write-Host "Examples:" -ForegroundColor Yellow
    Write-Host "  .\codex.ps1 'Create a hello world app'" -ForegroundColor Gray
    Write-Host "  .\codex.ps1 'Mujhe ek calculator chahiye'" -ForegroundColor Gray
    Write-Host "  .\codex.ps1 'List files and analyze code'" -ForegroundColor Gray
    Write-Host "  .\codex.ps1 --quiet 'Fix all bugs in my code'" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Available Features:" -ForegroundColor Yellow
    Write-Host "  • Chain-of-thought reasoning with self-critique" -ForegroundColor Gray
    Write-Host "  • Advanced file operations (create/read/write/delete/search)" -ForegroundColor Gray
    Write-Host "  • Code analysis with AST parsing (20+ languages)" -ForegroundColor Gray
    Write-Host "  • Web search integration for documentation" -ForegroundColor Gray
    Write-Host "  • Multi-step workflow execution" -ForegroundColor Gray
    Write-Host "  • Natural language processing (Hindi/English)" -ForegroundColor Gray
    Write-Host "  • Autonomous debugging and error fixing" -ForegroundColor Gray
    Write-Host "  • Context-aware refactoring and optimization" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Options:" -ForegroundColor Yellow
    Write-Host "  --quiet                              : Non-interactive mode" -ForegroundColor Gray
    Write-Host "  --dangerously-auto-approve-everything : Auto-approve all actions" -ForegroundColor Gray
    Write-Host "  --help                               : Show detailed help" -ForegroundColor Gray
    Write-Host ""
    
    # Show help
    & node .\codex-cli\dist\cli.js --provider gemini --help
} else {
    # Execute with arguments
    & node .\codex-cli\dist\cli.js --provider gemini $Arguments
}
