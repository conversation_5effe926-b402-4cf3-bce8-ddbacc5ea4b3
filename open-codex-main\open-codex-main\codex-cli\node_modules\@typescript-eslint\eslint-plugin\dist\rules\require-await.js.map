{"version": 3, "file": "require-await.js", "sourceRoot": "", "sources": ["../../src/rules/require-await.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AAGxC,kCAMiB;AAcjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,sFAAsF;YACxF,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,IAAI;SACtB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,YAAY,EAAE,qCAAqC;SACpD;KACF;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,IAAI,SAAS,GAAqB,IAAI,CAAC;QAEvC;;WAEG;QACH,SAAS,aAAa,CAAC,IAAkB;YACvC,SAAS,GAAG;gBACV,KAAK,EAAE,SAAS;gBAChB,QAAQ,EAAE,KAAK;gBACf,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,KAAK,EAAE,IAAI,CAAC,SAAS,IAAI,KAAK;gBAC9B,YAAY,EAAE,KAAK;aACpB,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAkB;YACtC,wBAAwB,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBACxC,8EAA8E;gBAC9E,OAAO;YACT,CAAC;YAED,IACE,IAAI,CAAC,KAAK;gBACV,CAAC,SAAS,CAAC,QAAQ;gBACnB,CAAC,eAAe,CAAC,IAAI,CAAC;gBACtB,CAAC,CAAC,SAAS,CAAC,KAAK,IAAI,SAAS,CAAC,YAAY,CAAC,EAC5C,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,GAAG,EAAE,IAAA,yBAAkB,EAAC,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC;oBACjD,SAAS,EAAE,cAAc;oBACzB,IAAI,EAAE;wBACJ,IAAI,EAAE,IAAA,qBAAc,EAAC,IAAA,8BAAuB,EAAC,IAAI,CAAC,CAAC;qBACpD;iBACF,CAAC,CAAC;YACL,CAAC;YAED,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC;QAC9B,CAAC;QAED;;WAEG;QACH,SAAS,cAAc,CAAC,IAAa;YACnC,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE7C,OAAO,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QACrD,CAAC;QAED;;WAEG;QACH,SAAS,cAAc;YACrB,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO;YACT,CAAC;YACD,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC;QAC5B,CAAC;QAED;;;;;WAKG;QACH,SAAS,oBAAoB,CAAC,IAA8B;YAC1D,IAAI,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACxC,OAAO;YACT,CAAC;YAED,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO,EAAE,CAAC;gBAClD,sEAAsE;gBACtE,0CAA0C;gBAC1C,OAAO;YACT,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACnB,IAAI,cAAc,CAAC,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;oBACtE,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;gBAChC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,YAAY,GAAG,6BAA6B,CAAC,IAAI,CAAC,CAAC;YACzD,KAAK,MAAM,IAAI,IAAI,YAAY,EAAE,CAAC;gBAChC,MAAM,aAAa,GAAG,OAAO,CAAC,gCAAgC,CAC5D,IAAI,EACJ,eAAe,EACf,OAAO,CACR,CAAC;gBACF,IAAI,aAAa,KAAK,SAAS,EAAE,CAAC;oBAChC,SAAS,CAAC,YAAY,GAAG,IAAI,CAAC;oBAC9B,MAAM;gBACR,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YACtC,0BAA0B,EAAE,YAAY;YACxC,yBAAyB,EAAE,YAAY;YACvC,8BAA8B,EAAE,YAAY;YAE5C,eAAe,EAAE,cAAc;YAC/B,2CAA2C,EAAE,cAAc;YAC3D,8BAA8B,EAAE,cAAc;YAC9C,eAAe,EAAE,oBAAoB;YAErC,wCAAwC;YACxC,gEAAgE;YAChE,+EAA+E,CAC7E,IAGC;gBAED,MAAM,UAAU,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAC5D,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC/B,cAAc,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,uDAAuD;gBACvD,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;oBAC5D,OAAO;gBACT,CAAC;gBAED,MAAM,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAChE,IAAI,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC;oBAC7C,cAAc,EAAE,CAAC;gBACnB,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,eAAe,CAAC,IAAkB;IACzC,OAAO,CACL,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;QAChD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAC5B,CAAC;AACJ,CAAC;AAED,SAAS,6BAA6B,CAAC,IAAa;IAClD,IAAI,IAAI,CAAC,qBAAqB,EAAE,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,6BAA6B,CAAC,CAAC;IAC3D,CAAC;IACD,OAAO,CAAC,IAAI,CAAC,CAAC;AAChB,CAAC"}