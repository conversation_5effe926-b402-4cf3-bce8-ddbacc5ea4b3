@echo off
REM Enhanced AI Development Agent Launcher
REM Usage: codex.bat "your prompt here"

set GOOGLE_GENERATIVE_AI_API_KEY=AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs

if "%1"=="" (
    echo 🚀 Enhanced AI Development Agent
    echo ================================
    echo Usage: codex.bat "your prompt here"
    echo.
    echo Examples:
    echo   codex.bat "Create a hello world app"
    echo   codex.bat "Mujhe ek calculator chahiye"
    echo   codex.bat "List files and analyze code"
    echo.
    echo Available options:
    echo   --quiet     : Non-interactive mode
    echo   --help      : Show help
    echo.
    node .\codex-cli\dist\cli.js --provider gemini --help
) else (
    node .\codex-cli\dist\cli.js --provider gemini %*
)
