/**
 * Utility functions for handling platform-specific commands
 */

import { log, isLoggingEnabled } from "./log.js";

/**
 * Map of Unix commands to their Windows equivalents
 */
const COMMAND_MAP: Record<string, string> = {
  ls: "dir",
  grep: "findstr",
  cat: "type",
  rm: "del",
  cp: "copy",
  mv: "move",
  touch: "type nul >", // Fixed Windows touch command
  mkdir: "md",
  pwd: "cd",
  which: "where",
  // echo is handled specially in adaptCommandForPlatform
};

/**
 * Map of common Unix command options to their Windows equivalents
 */
const OPTION_MAP: Record<string, Record<string, string>> = {
  ls: {
    "-l": "/p",
    "-a": "/a",
    "-R": "/s",
  },
  grep: {
    "-i": "/i",
    "-r": "/s",
  },
};

/**
 * Adapts a command for the current platform.
 * On Windows, this will translate Unix commands to their Windows equivalents.
 * On Unix-like systems, this will return the original command.
 *
 * @param command The command array to adapt
 * @returns The adapted command array
 */
export function adaptCommandForPlatform(command: Array<string>): Array<string> {
  // If not on Windows, return the original command
  if (process.platform !== "win32") {
    return command;
  }

  // Nothing to adapt if the command is empty
  if (command.length === 0) {
    return command;
  }

  const cmd = command[0];

  // Special handling for common commands on Windows
  if (cmd === "echo") {
    if (isLoggingEnabled()) {
      log(`Adapting echo command for Windows platform`);
    }
    // Use cmd /c echo for better compatibility and proper escaping
    const echoText = command.slice(1).join(" ");
    return ["cmd", "/c", "echo", echoText];
  }

  // Special handling for ls command
  if (cmd === "ls") {
    if (isLoggingEnabled()) {
      log(`Adapting ls command for Windows platform`);
    }
    // Convert ls options to dir options
    const dirArgs = ["cmd", "/c", "dir"];
    for (let i = 1; i < command.length; i++) {
      const arg = command[i];
      if (arg === "-l") {
        // -l flag doesn't have direct equivalent, dir shows details by default
        continue;
      } else if (arg === "-a") {
        dirArgs.push("/a");
      } else if (arg === "-R") {
        dirArgs.push("/s");
      } else if (!arg.startsWith("-")) {
        dirArgs.push(arg);
      }
    }
    return dirArgs;
  }

  // Special handling for cat command
  if (cmd === "cat") {
    if (isLoggingEnabled()) {
      log(`Adapting cat command for Windows platform`);
    }
    return ["cmd", "/c", "type", ...command.slice(1)];
  }

  // Special handling for pwd command
  if (cmd === "pwd") {
    if (isLoggingEnabled()) {
      log(`Adapting pwd command for Windows platform`);
    }
    return ["cmd", "/c", "cd"];
  }

  // If cmd is undefined or the command doesn't need adaptation, return it as is
  if (!cmd || !COMMAND_MAP[cmd]) {
    // For unknown commands, wrap them with cmd /c for better compatibility
    if (isLoggingEnabled()) {
      log(`Wrapping unknown command '${cmd}' with cmd /c for Windows compatibility`);
    }
    return ["cmd", "/c", ...command];
  }

  if (isLoggingEnabled()) {
    log(`Adapting command '${cmd}' for Windows platform`);
  }

  // Create a new command array with the adapted command
  const adaptedCommand = [...command];
  adaptedCommand[0] = COMMAND_MAP[cmd];

  // Adapt options if needed
  const optionsForCmd = OPTION_MAP[cmd];
  if (optionsForCmd) {
    for (let i = 1; i < adaptedCommand.length; i++) {
      const option = adaptedCommand[i];
      if (option && optionsForCmd[option]) {
        adaptedCommand[i] = optionsForCmd[option];
      }
    }
  }

  if (isLoggingEnabled()) {
    log(`Adapted command: ${adaptedCommand.join(" ")}`);
  }

  return adaptedCommand;
}
