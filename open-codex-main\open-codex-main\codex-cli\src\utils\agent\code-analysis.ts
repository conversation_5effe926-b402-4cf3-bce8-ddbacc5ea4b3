import * as fs from "fs";
import * as path from "path";
import { promisify } from "util";
import { isLoggingEnabled, log } from "./log.js";

const readFile = promisify(fs.readFile);

export interface CodeAnalysisArgs {
  operation: "parse" | "extract_functions" | "extract_classes" | "find_imports" | "analyze_structure";
  file_path: string;
  language?: string;
}

export interface CodeAnalysisResult {
  success: boolean;
  data?: any;
  error?: string;
  metadata?: {
    operation: string;
    file_path: string;
    language: string;
    lines_of_code?: number;
  };
}

export interface FunctionInfo {
  name: string;
  line_start: number;
  line_end: number;
  parameters: string[];
  return_type?: string;
  docstring?: string;
  complexity?: number;
}

export interface ClassInfo {
  name: string;
  line_start: number;
  line_end: number;
  methods: FunctionInfo[];
  properties: string[];
  inheritance?: string[];
  docstring?: string;
}

export interface ImportInfo {
  module: string;
  alias?: string;
  items?: string[];
  line: number;
  type: "import" | "from_import" | "require" | "include";
}

export class CodeAnalysisHandler {
  constructor() {
    if (isLoggingEnabled()) {
      log("CodeAnalysisHandler initialized");
    }
  }

  async handleCodeAnalysis(args: CodeAnalysisArgs): Promise<CodeAnalysisResult> {
    try {
      const resolvedPath = path.resolve(args.file_path);
      const content = await readFile(resolvedPath, "utf8");
      const language = args.language || this.detectLanguage(resolvedPath);
      
      if (isLoggingEnabled()) {
        log(`Code analysis: ${args.operation} on ${resolvedPath} (${language})`);
      }

      switch (args.operation) {
        case "parse":
          return await this.parseCode(content, language, resolvedPath);
        case "extract_functions":
          return await this.extractFunctions(content, language, resolvedPath);
        case "extract_classes":
          return await this.extractClasses(content, language, resolvedPath);
        case "find_imports":
          return await this.findImports(content, language, resolvedPath);
        case "analyze_structure":
          return await this.analyzeStructure(content, language, resolvedPath);
        default:
          throw new Error(`Unknown operation: ${args.operation}`);
      }
    } catch (error) {
      if (isLoggingEnabled()) {
        log(`Code analysis error: ${error}`);
      }
      return {
        success: false,
        error: String(error),
        metadata: {
          operation: args.operation,
          file_path: args.file_path,
          language: args.language || "unknown",
        },
      };
    }
  }

  private detectLanguage(filePath: string): string {
    const ext = path.extname(filePath).toLowerCase();
    const languageMap: Record<string, string> = {
      ".js": "javascript",
      ".jsx": "javascript",
      ".ts": "typescript",
      ".tsx": "typescript",
      ".py": "python",
      ".java": "java",
      ".cpp": "cpp",
      ".c": "c",
      ".cs": "csharp",
      ".go": "go",
      ".rs": "rust",
      ".php": "php",
      ".rb": "ruby",
      ".swift": "swift",
      ".kt": "kotlin",
      ".dart": "dart",
      ".scala": "scala",
      ".clj": "clojure",
      ".hs": "haskell",
      ".ml": "ocaml",
      ".fs": "fsharp",
      ".elm": "elm",
      ".ex": "elixir",
      ".erl": "erlang",
      ".lua": "lua",
      ".r": "r",
      ".m": "matlab",
      ".jl": "julia",
      ".nim": "nim",
      ".cr": "crystal",
      ".d": "d",
      ".zig": "zig",
    };
    
    return languageMap[ext] || "unknown";
  }

  private async parseCode(content: string, language: string, filePath: string): Promise<CodeAnalysisResult> {
    const lines = content.split("\n");
    const linesOfCode = lines.filter(line => line.trim() && !line.trim().startsWith("//") && !line.trim().startsWith("#")).length;
    
    // Basic parsing without AST for now
    const structure = {
      total_lines: lines.length,
      lines_of_code: linesOfCode,
      blank_lines: lines.filter(line => !line.trim()).length,
      comment_lines: lines.filter(line => {
        const trimmed = line.trim();
        return trimmed.startsWith("//") || trimmed.startsWith("#") || trimmed.startsWith("/*") || trimmed.startsWith("*");
      }).length,
      file_size: content.length,
      encoding: "utf8",
    };

    return {
      success: true,
      data: structure,
      metadata: {
        operation: "parse",
        file_path: filePath,
        language,
        lines_of_code: linesOfCode,
      },
    };
  }

  private async extractFunctions(content: string, language: string, filePath: string): Promise<CodeAnalysisResult> {
    const functions: FunctionInfo[] = [];
    const lines = content.split("\n");
    
    // Language-specific function extraction patterns
    const patterns = this.getFunctionPatterns(language);
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      for (const pattern of patterns) {
        const match = line.match(pattern.regex);
        if (match) {
          const func: FunctionInfo = {
            name: match[pattern.nameGroup] || "unknown",
            line_start: i + 1,
            line_end: this.findFunctionEnd(lines, i, language),
            parameters: this.extractParameters(match[pattern.paramsGroup] || ""),
            docstring: this.extractDocstring(lines, i, language),
          };
          functions.push(func);
        }
      }
    }

    return {
      success: true,
      data: functions,
      metadata: {
        operation: "extract_functions",
        file_path: filePath,
        language,
        lines_of_code: lines.length,
      },
    };
  }

  private async extractClasses(content: string, language: string, filePath: string): Promise<CodeAnalysisResult> {
    const classes: ClassInfo[] = [];
    const lines = content.split("\n");
    
    // Language-specific class extraction patterns
    const patterns = this.getClassPatterns(language);
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      for (const pattern of patterns) {
        const match = line.match(pattern.regex);
        if (match) {
          const classInfo: ClassInfo = {
            name: match[pattern.nameGroup] || "unknown",
            line_start: i + 1,
            line_end: this.findClassEnd(lines, i, language),
            methods: [],
            properties: [],
            inheritance: this.extractInheritance(match[pattern.inheritanceGroup] || ""),
            docstring: this.extractDocstring(lines, i, language),
          };
          classes.push(classInfo);
        }
      }
    }

    return {
      success: true,
      data: classes,
      metadata: {
        operation: "extract_classes",
        file_path: filePath,
        language,
        lines_of_code: lines.length,
      },
    };
  }

  private async findImports(content: string, language: string, filePath: string): Promise<CodeAnalysisResult> {
    const imports: ImportInfo[] = [];
    const lines = content.split("\n");
    
    // Language-specific import patterns
    const patterns = this.getImportPatterns(language);
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();
      for (const pattern of patterns) {
        const match = line.match(pattern.regex);
        if (match) {
          const importInfo: ImportInfo = {
            module: match[pattern.moduleGroup] || "unknown",
            alias: match[pattern.aliasGroup],
            items: pattern.itemsGroup ? this.parseImportItems(match[pattern.itemsGroup] || "") : undefined,
            line: i + 1,
            type: pattern.type,
          };
          imports.push(importInfo);
        }
      }
    }

    return {
      success: true,
      data: imports,
      metadata: {
        operation: "find_imports",
        file_path: filePath,
        language,
        lines_of_code: lines.length,
      },
    };
  }

  private async analyzeStructure(content: string, language: string, filePath: string): Promise<CodeAnalysisResult> {
    // Combine all analysis operations
    const parseResult = await this.parseCode(content, language, filePath);
    const functionsResult = await this.extractFunctions(content, language, filePath);
    const classesResult = await this.extractClasses(content, language, filePath);
    const importsResult = await this.findImports(content, language, filePath);

    const structure = {
      file_info: parseResult.data,
      functions: functionsResult.data,
      classes: classesResult.data,
      imports: importsResult.data,
      complexity_score: this.calculateComplexity(content, language),
      maintainability_index: this.calculateMaintainability(content, language),
    };

    return {
      success: true,
      data: structure,
      metadata: {
        operation: "analyze_structure",
        file_path: filePath,
        language,
        lines_of_code: parseResult.data?.lines_of_code || 0,
      },
    };
  }

  private getFunctionPatterns(language: string) {
    const patterns: Record<string, Array<{ regex: RegExp; nameGroup: number; paramsGroup: number }>> = {
      javascript: [
        { regex: /function\s+(\w+)\s*\(([^)]*)\)/, nameGroup: 1, paramsGroup: 2 },
        { regex: /const\s+(\w+)\s*=\s*\(([^)]*)\)\s*=>/, nameGroup: 1, paramsGroup: 2 },
        { regex: /(\w+)\s*:\s*function\s*\(([^)]*)\)/, nameGroup: 1, paramsGroup: 2 },
      ],
      typescript: [
        { regex: /function\s+(\w+)\s*\(([^)]*)\)/, nameGroup: 1, paramsGroup: 2 },
        { regex: /const\s+(\w+)\s*=\s*\(([^)]*)\)\s*=>/, nameGroup: 1, paramsGroup: 2 },
        { regex: /(\w+)\s*\(([^)]*)\)\s*:\s*\w+/, nameGroup: 1, paramsGroup: 2 },
      ],
      python: [
        { regex: /def\s+(\w+)\s*\(([^)]*)\)/, nameGroup: 1, paramsGroup: 2 },
        { regex: /async\s+def\s+(\w+)\s*\(([^)]*)\)/, nameGroup: 1, paramsGroup: 2 },
      ],
      java: [
        { regex: /(?:public|private|protected)?\s*(?:static)?\s*\w+\s+(\w+)\s*\(([^)]*)\)/, nameGroup: 1, paramsGroup: 2 },
      ],
      cpp: [
        { regex: /\w+\s+(\w+)\s*\(([^)]*)\)/, nameGroup: 1, paramsGroup: 2 },
      ],
    };
    
    return patterns[language] || patterns.javascript;
  }

  private getClassPatterns(language: string) {
    const patterns: Record<string, Array<{ regex: RegExp; nameGroup: number; inheritanceGroup?: number }>> = {
      javascript: [
        { regex: /class\s+(\w+)(?:\s+extends\s+(\w+))?/, nameGroup: 1, inheritanceGroup: 2 },
      ],
      typescript: [
        { regex: /class\s+(\w+)(?:\s+extends\s+(\w+))?(?:\s+implements\s+([^{]+))?/, nameGroup: 1, inheritanceGroup: 2 },
      ],
      python: [
        { regex: /class\s+(\w+)(?:\(([^)]+)\))?:/, nameGroup: 1, inheritanceGroup: 2 },
      ],
      java: [
        { regex: /(?:public|private)?\s*class\s+(\w+)(?:\s+extends\s+(\w+))?/, nameGroup: 1, inheritanceGroup: 2 },
      ],
      cpp: [
        { regex: /class\s+(\w+)(?:\s*:\s*(?:public|private|protected)\s+(\w+))?/, nameGroup: 1, inheritanceGroup: 2 },
      ],
    };
    
    return patterns[language] || patterns.javascript;
  }

  private getImportPatterns(language: string) {
    const patterns: Record<string, Array<{ regex: RegExp; moduleGroup: number; aliasGroup?: number; itemsGroup?: number; type: ImportInfo["type"] }>> = {
      javascript: [
        { regex: /import\s+(\w+)\s+from\s+['"]([^'"]+)['"]/, moduleGroup: 2, aliasGroup: 1, type: "import" },
        { regex: /import\s*\{\s*([^}]+)\s*\}\s*from\s+['"]([^'"]+)['"]/, moduleGroup: 2, itemsGroup: 1, type: "from_import" },
        { regex: /const\s+(\w+)\s*=\s*require\(['"]([^'"]+)['"]\)/, moduleGroup: 2, aliasGroup: 1, type: "require" },
      ],
      typescript: [
        { regex: /import\s+(\w+)\s+from\s+['"]([^'"]+)['"]/, moduleGroup: 2, aliasGroup: 1, type: "import" },
        { regex: /import\s*\{\s*([^}]+)\s*\}\s*from\s+['"]([^'"]+)['"]/, moduleGroup: 2, itemsGroup: 1, type: "from_import" },
      ],
      python: [
        { regex: /import\s+(\w+)(?:\s+as\s+(\w+))?/, moduleGroup: 1, aliasGroup: 2, type: "import" },
        { regex: /from\s+(\w+)\s+import\s+(.+)/, moduleGroup: 1, itemsGroup: 2, type: "from_import" },
      ],
      java: [
        { regex: /import\s+([^;]+);/, moduleGroup: 1, type: "import" },
      ],
      cpp: [
        { regex: /#include\s*[<"]([^>"]+)[>"]/, moduleGroup: 1, type: "include" },
      ],
    };
    
    return patterns[language] || patterns.javascript;
  }

  private findFunctionEnd(lines: string[], start: number, language: string): number {
    let braceCount = 0;
    let inFunction = false;
    
    for (let i = start; i < lines.length; i++) {
      const line = lines[i];
      
      if (line.includes("{")) {
        braceCount += (line.match(/\{/g) || []).length;
        inFunction = true;
      }
      
      if (line.includes("}")) {
        braceCount -= (line.match(/\}/g) || []).length;
        if (inFunction && braceCount === 0) {
          return i + 1;
        }
      }
      
      // For Python, use indentation
      if (language === "python" && i > start && line.trim() && !line.startsWith(" ") && !line.startsWith("\t")) {
        return i;
      }
    }
    
    return lines.length;
  }

  private findClassEnd(lines: string[], start: number, language: string): number {
    return this.findFunctionEnd(lines, start, language);
  }

  private extractParameters(paramString: string): string[] {
    if (!paramString.trim()) return [];
    return paramString.split(",").map(p => p.trim()).filter(p => p);
  }

  private extractInheritance(inheritanceString: string): string[] {
    if (!inheritanceString.trim()) return [];
    return inheritanceString.split(",").map(i => i.trim()).filter(i => i);
  }

  private extractDocstring(lines: string[], functionStart: number, language: string): string | undefined {
    // Look for docstring before or after function declaration
    const searchLines = lines.slice(Math.max(0, functionStart - 3), Math.min(lines.length, functionStart + 5));
    
    for (const line of searchLines) {
      const trimmed = line.trim();
      if (trimmed.startsWith("/**") || trimmed.startsWith('"""') || trimmed.startsWith("'''")) {
        return trimmed;
      }
    }
    
    return undefined;
  }

  private parseImportItems(itemsString: string): string[] {
    return itemsString.split(",").map(item => item.trim()).filter(item => item);
  }

  private calculateComplexity(content: string, language: string): number {
    // Simple cyclomatic complexity calculation
    const complexityKeywords = ["if", "else", "while", "for", "switch", "case", "catch", "&&", "||"];
    let complexity = 1; // Base complexity
    
    for (const keyword of complexityKeywords) {
      const regex = new RegExp(`\\b${keyword}\\b`, "g");
      const matches = content.match(regex);
      if (matches) {
        complexity += matches.length;
      }
    }
    
    return complexity;
  }

  private calculateMaintainability(content: string, language: string): number {
    // Simple maintainability index calculation
    const lines = content.split("\n");
    const linesOfCode = lines.filter(line => line.trim() && !line.trim().startsWith("//")).length;
    const complexity = this.calculateComplexity(content, language);

    // Simplified maintainability index
    const maintainabilityIndex = Math.max(0, 171 - 5.2 * Math.log(linesOfCode) - 0.23 * complexity);

    return Math.round(maintainabilityIndex);
  }
}
