# Simple diagnostic test for Open-Codex
Write-Host "🔍 SIMPLE OPEN-CODEX TEST" -ForegroundColor Cyan

# Set environment variable
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"
Write-Host "✓ Environment variable set" -ForegroundColor Green

# Test 1: Help command
Write-Host "`n📋 Testing help command..." -ForegroundColor Yellow
$helpResult = & node dist/cli.js --help 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Help command works" -ForegroundColor Green
} else {
    Write-Host "❌ Help command failed" -ForegroundColor Red
    Write-Host "Error: $helpResult" -ForegroundColor Red
}

# Test 2: Provider test
Write-Host "`n🔧 Testing provider configuration..." -ForegroundColor Yellow
$providerResult = & node dist/cli.js --provider gemini --help 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Provider configuration works" -ForegroundColor Green
} else {
    Write-Host "❌ Provider test failed" -ForegroundColor Red
    Write-Host "Error: $providerResult" -ForegroundColor Red
}

# Test 3: Simple quiet mode test
Write-Host "`n⚡ Testing quiet mode..." -ForegroundColor Yellow
$quietResult = & node dist/cli.js --provider gemini --quiet "echo hello" 2>&1
Write-Host "Quiet mode result: $quietResult" -ForegroundColor Gray

Write-Host "`n🏁 TEST COMPLETE" -ForegroundColor Cyan
