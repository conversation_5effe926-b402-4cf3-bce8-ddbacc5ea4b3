# Test script for updated Open-Codex system
Write-Host "🧪 Testing Updated Open-Codex System..." -ForegroundColor Green

# Set environment variable
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"

Write-Host "✓ Environment variable set" -ForegroundColor Green

# Test help command
Write-Host "`n📋 Testing help command..." -ForegroundColor Cyan
& node dist/cli.js --help

Write-Host "`n🎯 Testing with updated Gemini model..." -ForegroundColor Cyan
Write-Host "Note: Using gemini-2.0-flash as default model" -ForegroundColor Yellow

# Test with a simple prompt (but expect rate limit)
Write-Host "`n🚀 Testing simple prompt..." -ForegroundColor Cyan
Write-Host "Expected: Rate limit error (which means API is working)" -ForegroundColor Yellow

& node dist/cli.js --provider gemini --model gemini-2.0-flash "What AI model are you?"
