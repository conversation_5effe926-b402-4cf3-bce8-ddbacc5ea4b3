# Comprehensive diagnostic script for Open-Codex CLI system
Write-Host "🔍 COMPREHENSIVE OPEN-CODEX DIAGNOSTIC" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan

# Set environment variable
$env:GOOGLE_GENERATIVE_AI_API_KEY = "AIzaSyDajZhCDYy6RqJdqgRpgtUcBeXbtpvgcjs"
Write-Host "✓ Environment variable set" -ForegroundColor Green

# Test 1: Basic CLI functionality
Write-Host "`n📋 TEST 1: Basic CLI Help..." -ForegroundColor Yellow
try {
    $helpOutput = & node dist/cli.js --help 2>&1
    Write-Host "✓ Help command works" -ForegroundColor Green
}
catch {
    Write-Host "❌ Help command failed: $_" -ForegroundColor Red
}

# Test 2: Provider configuration
Write-Host "`n🔧 TEST 2: Provider Configuration..." -ForegroundColor Yellow
try {
    $providerOutput = & node dist/cli.js --provider gemini --help 2>&1
    Write-Host "✓ Gemini provider recognized" -ForegroundColor Green
}
catch {
    Write-Host "❌ Provider test failed: $_" -ForegroundColor Red
}

# Test 3: Simple command execution test
Write-Host "`n⚡ TEST 3: Command Execution Test..." -ForegroundColor Yellow
Write-Host "Testing with a simple shell command..." -ForegroundColor Gray

# Create a test file for command execution
$testFile = "test-command.txt"
"Test content for command execution" | Out-File -FilePath $testFile -Encoding UTF8

try {
    # Test with a simple file listing command
    $result = & node dist/cli.js --provider gemini --quiet "List the files in the current directory and tell me what you see" 2>&1
    Write-Host "✓ Command execution test completed" -ForegroundColor Green
    Write-Host "Response: $result" -ForegroundColor Gray
}
catch {
    Write-Host "❌ Command execution failed: $_" -ForegroundColor Red
}
finally {
    # Clean up test file
    if (Test-Path $testFile) {
        Remove-Item $testFile -Force
    }
}

# Test 4: Tool call functionality
Write-Host "`n🛠️ TEST 4: Tool Call Functionality..." -ForegroundColor Yellow
Write-Host "Testing if the system can handle tool calls..." -ForegroundColor Gray

try {
    # Test with a command that should trigger tool calls
    $result = & node dist/cli.js --provider gemini --quiet "Create a simple hello.txt file with 'Hello World' content" 2>&1
    Write-Host "✓ Tool call test completed" -ForegroundColor Green

    # Check if file was created
    if (Test-Path "hello.txt") {
        Write-Host "✓ File creation successful" -ForegroundColor Green
        $content = Get-Content "hello.txt" -Raw
        Write-Host "File content: $content" -ForegroundColor Gray
        Remove-Item "hello.txt" -Force
    }
    else {
        Write-Host "⚠️ File was not created (may be due to approval mode)" -ForegroundColor Yellow
    }
}
catch {
    Write-Host "❌ Tool call test failed: $_" -ForegroundColor Red
}

# Test 5: Interactive mode check
Write-Host "`n🎮 TEST 5: Interactive Mode Check..." -ForegroundColor Yellow
Write-Host "Testing interactive mode startup..." -ForegroundColor Gray

# Test 6: Configuration validation
Write-Host "`n⚙️ TEST 6: Configuration Validation..." -ForegroundColor Yellow
Write-Host "Checking configuration files and settings..." -ForegroundColor Gray

# Check if config directory exists
$configDir = Join-Path $env:USERPROFILE ".codex"
if (Test-Path $configDir) {
    Write-Host "✓ Config directory exists: $configDir" -ForegroundColor Green
    $configFile = Join-Path $configDir "config.json"
    if (Test-Path $configFile) {
        Write-Host "✓ Config file exists" -ForegroundColor Green
        $config = Get-Content $configFile -Raw | ConvertFrom-Json
        Write-Host "Config: $($config | ConvertTo-Json -Compress)" -ForegroundColor Gray
    }
    else {
        Write-Host "⚠️ Config file missing" -ForegroundColor Yellow
    }
}
else {
    Write-Host "⚠️ Config directory missing" -ForegroundColor Yellow
}

Write-Host "`n🏁 DIAGNOSTIC COMPLETE" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan
