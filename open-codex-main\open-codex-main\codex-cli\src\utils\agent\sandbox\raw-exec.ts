import type { ExecResult } from "./interface";
import type {
  ChildProcess,
  SpawnOptions,
  SpawnOptionsWithStdioTuple,
  StdioNull,
  StdioPipe,
} from "child_process";

import { log, isLoggingEnabled } from "../log.js";
import { adaptCommandForPlatform } from "../platform-commands.js";
import { spawn } from "child_process";
import * as os from "os";

const MAX_BUFFER = 1024 * 100; // 100 KB

/**
 * This function should never return a rejected promise: errors should be
 * mapped to a non-zero exit code and the error message should be in stderr.
 */
export function exec(
  command: Array<string>,
  options: SpawnOptions,
  _writableRoots: Array<string>,
  abortSignal?: AbortSignal,
): Promise<ExecResult> {
  return execWithFallback(command, options, _writableRoots, abortSignal);
}

async function execWithFallback(
  command: Array<string>,
  options: SpawnOptions,
  _writableRoots: Array<string>,
  abortSignal?: AbortSignal,
): Promise<ExecResult> {
  // Adapt command for the current platform (e.g., convert 'ls' to 'dir' on Windows)
  const adaptedCommand = adaptCommandForPlatform(command);

  if (
    isLoggingEnabled() &&
    JSON.stringify(adaptedCommand) !== JSON.stringify(command)
  ) {
    log(
      `Command adapted for platform: ${command.join(
        " ",
      )} -> ${adaptedCommand.join(" ")}`,
    );
  }

  // Try the adapted command first
  try {
    const result = await execInternal(adaptedCommand, options, _writableRoots, abortSignal);

    // If command failed with ENOENT on Windows, try fallback
    if (process.platform === "win32" && result.exitCode !== 0 && result.stderr.includes("ENOENT")) {
      if (isLoggingEnabled()) {
        log(`Command failed with ENOENT, trying fallback for: ${adaptedCommand.join(" ")}`);
      }

      // Try with powershell as fallback
      const fallbackCommand = ["powershell", "-Command", command.join(" ")];
      if (isLoggingEnabled()) {
        log(`Trying PowerShell fallback: ${fallbackCommand.join(" ")}`);
      }

      const fallbackResult = await execInternal(fallbackCommand, options, _writableRoots, abortSignal);
      if (fallbackResult.exitCode === 0 || fallbackResult.stdout.length > 0) {
        return fallbackResult;
      }
    }

    return result;
  } catch (error) {
    if (isLoggingEnabled()) {
      log(`execWithFallback error: ${error}`);
    }
    return {
      stdout: "",
      stderr: String(error),
      exitCode: 1,
    };
  }
}

function execInternal(
  adaptedCommand: Array<string>,
  options: SpawnOptions,
  _writableRoots: Array<string>,
  abortSignal?: AbortSignal,
): Promise<ExecResult> {

  const prog = adaptedCommand[0];
  if (typeof prog !== "string") {
    return Promise.resolve({
      stdout: "",
      stderr: "command[0] is not a string",
      exitCode: 1,
    });
  }

  // Enhanced Windows command handling
  if (process.platform === "win32") {
    // List of commands that should work directly on Windows
    const nativeWindowsCommands = ["cmd", "powershell", "where", "tasklist", "systeminfo"];
    const cmdBuiltins = ["dir", "type", "del", "copy", "move", "md", "cd", "echo", "cls"];

    if (isLoggingEnabled()) {
      log(`Windows: Checking command '${prog}' for compatibility`);
    }

    // If it's already a cmd or powershell command, use it directly
    if (nativeWindowsCommands.includes(prog.toLowerCase())) {
      if (isLoggingEnabled()) {
        log(`Windows: Using native command '${prog}' directly`);
      }
    }
    // If it's a cmd builtin and not already wrapped, ensure it's wrapped with cmd /c
    else if (cmdBuiltins.includes(prog.toLowerCase()) && adaptedCommand[0] !== "cmd") {
      if (isLoggingEnabled()) {
        log(`Windows: Wrapping builtin command '${prog}' with cmd /c`);
      }
      const wrappedCommand = ["cmd", "/c", ...adaptedCommand];
      return exec(wrappedCommand, options, _writableRoots, abortSignal);
    }
    // For other commands, they should already be adapted by platform-commands.ts
    else if (!prog.includes("\\") && !prog.includes(".exe") && adaptedCommand[0] !== "cmd") {
      if (isLoggingEnabled()) {
        log(`Windows: Command '${prog}' not recognized, may fail. Consider using full path or cmd /c wrapper.`);
      }
    }
  }

  // We use spawn() instead of exec() or execFile() so that we can set the
  // stdio options to "ignore" for stdin. Ripgrep has a heuristic where it
  // may try to read from stdin as explained here:
  //
  // https://github.com/BurntSushi/ripgrep/blob/e2362d4d5185d02fa857bf381e7bd52e66fafc73/crates/core/flags/hiargs.rs#L1101-L1103
  //
  // This can be a problem because if you save the following to a file and
  // run it with `node`, it will hang forever:
  //
  // ```
  // const {execFile} = require('child_process');
  //
  // execFile('rg', ['foo'], (error, stdout, stderr) => {
  //   if (error) {
  //     console.error(`error: ${error}n\nstderr: ${stderr}`);
  //   } else {
  //     console.log(`stdout: ${stdout}`);
  //   }
  // });
  // ```
  //
  // Even if you pass `{stdio: ["ignore", "pipe", "pipe"] }` to execFile(), the
  // hang still happens as the `stdio` is seemingly ignored. Using spawn()
  // works around this issue.
  const fullOptions: SpawnOptionsWithStdioTuple<
    StdioNull,
    StdioPipe,
    StdioPipe
  > = {
    ...options,
    // Inherit any caller‑supplied stdio flags but force stdin to "ignore" so
    // the child never attempts to read from us (see lengthy comment above).
    stdio: ["ignore", "pipe", "pipe"],
    // Launch the child in its *own* process group so that we can later send a
    // single signal to the entire group – this reliably terminates not only
    // the immediate child but also any grandchildren it might have spawned
    // (think `bash -c "sleep 999"`).
    detached: true,
  };

  const child: ChildProcess = spawn(prog, adaptedCommand.slice(1), fullOptions);
  // If an AbortSignal is provided, ensure the spawned process is terminated
  // when the signal is triggered so that cancellations propagate down to any
  // long‑running child processes. We default to SIGTERM to give the process a
  // chance to clean up, falling back to SIGKILL if it does not exit in a
  // timely fashion.
  if (abortSignal) {
    const abortHandler = () => {
      if (isLoggingEnabled()) {
        log(`raw-exec: abort signal received – killing child ${child.pid}`);
      }
      const killTarget = (signal: NodeJS.Signals) => {
        if (!child.pid) {
          return;
        }
        try {
          try {
            // Send to the *process group* so grandchildren are included.
            process.kill(-child.pid, signal);
          } catch {
            // Fallback: kill only the immediate child (may leave orphans on
            // exotic kernels that lack process‑group semantics, but better
            // than nothing).
            try {
              child.kill(signal);
            } catch {
              /* ignore */
            }
          }
        } catch {
          /* already gone */
        }
      };

      // First try graceful termination.
      killTarget("SIGTERM");

      // Escalate to SIGKILL if the group refuses to die.
      setTimeout(() => {
        if (!child.killed) {
          killTarget("SIGKILL");
        }
      }, 2000).unref();
    };
    if (abortSignal.aborted) {
      abortHandler();
    } else {
      abortSignal.addEventListener("abort", abortHandler, { once: true });
    }
  }
  // If spawning the child failed (e.g. the executable could not be found)
  // `child.pid` will be undefined *and* an `error` event will be emitted on
  // the ChildProcess instance.  We intentionally do **not** bail out early
  // here.  Returning prematurely would leave the `error` event without a
  // listener which – in Node.js – results in an "Unhandled 'error' event"
  // process‑level exception that crashes the CLI.  Instead we continue with
  // the normal promise flow below where we are guaranteed to attach both the
  // `error` and `exit` handlers right away.  Either of those callbacks will
  // resolve the promise and translate the failure into a regular
  // ExecResult object so the rest of the agent loop can carry on gracefully.

  const stdoutChunks: Array<Buffer> = [];
  const stderrChunks: Array<Buffer> = [];
  let numStdoutBytes = 0;
  let numStderrBytes = 0;
  let hitMaxStdout = false;
  let hitMaxStderr = false;

  return new Promise<ExecResult>((resolve) => {
    // Add a timeout to prevent hanging
    const timeout = setTimeout(() => {
      if (isLoggingEnabled()) {
        log(`raw-exec: command timeout after 30 seconds, killing child ${child.pid}`);
      }
      if (child.pid && !child.killed) {
        try {
          process.kill(-child.pid, "SIGKILL");
        } catch {
          try {
            child.kill("SIGKILL");
          } catch {
            /* ignore */
          }
        }
      }
      resolve({
        stdout: Buffer.concat(stdoutChunks).toString("utf8"),
        stderr: "Command timed out after 30 seconds",
        exitCode: 124, // Standard timeout exit code
      });
    }, 30000);

    child.stdout?.on("data", (data: Buffer) => {
      if (!hitMaxStdout) {
        numStdoutBytes += data.length;
        if (numStdoutBytes <= MAX_BUFFER) {
          stdoutChunks.push(data);
        } else {
          hitMaxStdout = true;
        }
      }
    });
    child.stderr?.on("data", (data: Buffer) => {
      if (!hitMaxStderr) {
        numStderrBytes += data.length;
        if (numStderrBytes <= MAX_BUFFER) {
          stderrChunks.push(data);
        } else {
          hitMaxStderr = true;
        }
      }
    });
    child.on("exit", (code, signal) => {
      clearTimeout(timeout); // Clear the timeout when process exits normally
      const stdout = Buffer.concat(stdoutChunks).toString("utf8");
      const stderr = Buffer.concat(stderrChunks).toString("utf8");

      // Map (code, signal) to an exit code. We expect exactly one of the two
      // values to be non-null, but we code defensively to handle the case where
      // both are null.
      let exitCode: number;
      if (code != null) {
        exitCode = code;
      } else if (signal != null && signal in os.constants.signals) {
        const signalNum =
          os.constants.signals[signal as keyof typeof os.constants.signals];
        exitCode = 128 + signalNum;
      } else {
        exitCode = 1;
      }

      if (isLoggingEnabled()) {
        log(
          `raw-exec: child ${child.pid} exited code=${exitCode} signal=${signal}`,
        );
      }
      resolve({
        stdout,
        stderr,
        exitCode,
      });
    });

    child.on("error", (err) => {
      clearTimeout(timeout); // Clear the timeout when process errors

      if (isLoggingEnabled()) {
        log(`raw-exec: child process error: ${err}`);
      }

      // Enhanced error handling for Windows
      let errorMessage = String(err);
      if (process.platform === "win32" && err.message?.includes("ENOENT")) {
        errorMessage = `Command not found: '${prog}'. This may be because:
1. The command is not installed or not in PATH
2. The command name is incorrect for Windows
3. Try using the full path to the executable
Original error: ${err.message}`;
      }

      resolve({
        stdout: "",
        stderr: errorMessage,
        exitCode: 1,
      });
    });
  });
}
